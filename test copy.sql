-- 查询lpc空间下1-5月的工具使用量统计
select
  tool_name,
  count(*) as total_usage_count,
  count(distinct account_name) as user_count
from (
  select tool_name, toolbox_gameproject, account_name
  from blade_desktop_total_use
  where date between #timeRange_value.start# and #timeRange_value.end#
    -- 核心过滤条件：lpc空间
    and toolbox_abbr = 'lpc'
    -- 可选过滤条件
    and case when '' in (#select_toolbox_studio_cn_name#) then 1=1 else toolbox_studio_cn_name in (#select_toolbox_studio_cn_name#) end
    and case when '' in (#select_tool_app_name#) then 1=1 else app_name in (#select_tool_app_name#) end
    and case when '' in (#select_project_abbr#) then 1=1 else project_abbr in (#select_project_abbr#) end
    and case when '' in (#archived_toolbox_abbr#) then 1=1 else toolbox_abbr not in (#archived_toolbox_abbr#) end
    and case when '' in (#select_toolbox_gameproject_value#) then 1=1 else toolbox_gameproject in (#select_toolbox_gameproject_value#) end
    -- 数据清洗条件
    and tool_name != ''
    and toolbox_gameproject != ''
    and toolbox_gameproject != '$BLADE_TOOLBOX_GAMEPROJaECT'
)
group by tool_name
order by total_usage_count desc;