-- 查询lpc空间下1-5月的工具使用量统计（总量）
select
  tool_name,
  count(*) as total_usage_count,
  count(distinct account_name) as user_count
from (
  select tool_name, toolbox_gameproject, account_name
  from blade_desktop_total_use
  where date between #timeRange_value.start# and #timeRange_value.end#
    -- 核心过滤条件：lpc空间
    and toolbox_abbr = 'lpc'
    -- 可选过滤条件
    and case when '' in (#select_toolbox_studio_cn_name#) then 1=1 else toolbox_studio_cn_name in (#select_toolbox_studio_cn_name#) end
    and case when '' in (#select_tool_app_name#) then 1=1 else app_name in (#select_tool_app_name#) end
    and case when '' in (#select_project_abbr#) then 1=1 else project_abbr in (#select_project_abbr#) end
    and case when '' in (#archived_toolbox_abbr#) then 1=1 else toolbox_abbr not in (#archived_toolbox_abbr#) end
    and case when '' in (#select_toolbox_gameproject_value#) then 1=1 else toolbox_gameproject in (#select_toolbox_gameproject_value#) end
    -- 数据清洗条件
    and tool_name != ''
    and toolbox_gameproject != ''
    and toolbox_gameproject != '$BLADE_TOOLBOX_GAMEPROJaECT'
)
group by tool_name
order by total_usage_count desc;

-- ========================================
-- 折线图SQL：查询lpc空间下1-5月每个工具的月度使用量变化趋势
-- ========================================
select
  tool_name,
  date_format(date, '%Y-%m') as month_key,
  count(*) as monthly_usage_count,
  count(distinct account_name) as monthly_user_count
from blade_desktop_total_use
where date between #timeRange_value.start# and #timeRange_value.end#
  -- 核心过滤条件：lpc空间
  and toolbox_abbr = 'lpc'
  -- 可选过滤条件
  and case when '' in (#select_toolbox_studio_cn_name#) then 1=1 else toolbox_studio_cn_name in (#select_toolbox_studio_cn_name#) end
  and case when '' in (#select_tool_app_name#) then 1=1 else app_name in (#select_tool_app_name#) end
  and case when '' in (#select_project_abbr#) then 1=1 else project_abbr in (#select_project_abbr#) end
  and case when '' in (#archived_toolbox_abbr#) then 1=1 else toolbox_abbr not in (#archived_toolbox_abbr#) end
  and case when '' in (#select_toolbox_gameproject_value#) then 1=1 else toolbox_gameproject in (#select_toolbox_gameproject_value#) end
  -- 数据清洗条件
  and tool_name != ''
  and toolbox_gameproject != ''
  and toolbox_gameproject != '$BLADE_TOOLBOX_GAMEPROJaECT'
group by tool_name, date_format(date, '%Y-%m')
order by tool_name, month_key;

-- ========================================
-- 透视表格式SQL：更适合前端图表展示
-- ========================================
select
  tool_name,
  sum(case when date_format(date, '%Y-%m') = '2024-01' then 1 else 0 end) as jan_usage,
  sum(case when date_format(date, '%Y-%m') = '2024-02' then 1 else 0 end) as feb_usage,
  sum(case when date_format(date, '%Y-%m') = '2024-03' then 1 else 0 end) as mar_usage,
  sum(case when date_format(date, '%Y-%m') = '2024-04' then 1 else 0 end) as apr_usage,
  sum(case when date_format(date, '%Y-%m') = '2024-05' then 1 else 0 end) as may_usage,
  count(*) as total_usage
from blade_desktop_total_use
where date between '2024-01-01' and '2024-05-31'
  -- 核心过滤条件：lpc空间
  and toolbox_abbr = 'lpc'
  -- 可选过滤条件
  and case when '' in (#select_toolbox_studio_cn_name#) then 1=1 else toolbox_studio_cn_name in (#select_toolbox_studio_cn_name#) end
  and case when '' in (#select_tool_app_name#) then 1=1 else app_name in (#select_tool_app_name#) end
  and case when '' in (#select_project_abbr#) then 1=1 else project_abbr in (#select_project_abbr#) end
  and case when '' in (#archived_toolbox_abbr#) then 1=1 else toolbox_abbr not in (#archived_toolbox_abbr#) end
  and case when '' in (#select_toolbox_gameproject_value#) then 1=1 else toolbox_gameproject in (#select_toolbox_gameproject_value#) end
  -- 数据清洗条件
  and tool_name != ''
  and toolbox_gameproject != ''
  and toolbox_gameproject != '$BLADE_TOOLBOX_GAMEPROJaECT'
group by tool_name
order by total_usage desc;