# OAuth2 Login System - Vue3 + FastAPI

一个完整的OAuth2登录系统，使用Vue3前端和FastAPI后端实现。

## 🚀 功能特性

- ✅ JWT Token认证
- ✅ 用户登录/注册
- ✅ 受保护的路由
- ✅ 自动token验证
- ✅ 响应式设计
- ✅ 路由守卫
- ✅ 状态管理 (Pinia)

## 📁 项目结构

```
project/
├── backend/                 # FastAPI后端
│   ├── main.py             # 主应用文件
│   ├── auth.py             # 认证逻辑
│   ├── models.py           # 数据模型
│   └── requirements.txt    # Python依赖
├── frontend/               # Vue3前端
│   ├── src/
│   │   ├── components/     # Vue组件
│   │   ├── stores/         # Pinia状态管理
│   │   ├── router/         # 路由配置
│   │   └── main.js         # 入口文件
│   ├── package.json        # Node.js依赖
│   └── vite.config.js      # Vite配置
├── start-backend.bat       # 后端启动脚本
├── start-frontend.bat      # 前端启动脚本
└── README.md              # 说明文档
```

## 🛠️ 快速开始

### 方法1: 使用启动脚本 (推荐)

1. **启动后端服务器**
   ```bash
   # 双击运行或在命令行执行
   start-backend.bat
   ```

2. **启动前端开发服务器** (新开一个终端)
   ```bash
   # 双击运行或在命令行执行
   start-frontend.bat
   ```

### 方法2: 手动启动

#### 后端 (FastAPI)
```bash
cd backend
pip install -r requirements.txt
python main.py
```

#### 前端 (Vue3)
```bash
cd frontend
npm install
npm run dev
```

## 🌐 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 🔐 测试账号

系统预置了以下测试账号：

| 用户名 | 密码 | 说明 |
|--------|------|------|
| admin  | admin123 | 管理员账号 |
| user   | admin123 | 普通用户账号 |

## 📋 API端点

### 认证相关
- `POST /token` - 用户登录获取token
- `POST /register` - 用户注册
- `GET /users/me` - 获取当前用户信息

### 受保护的路由
- `GET /protected` - 测试受保护的API端点

## 🎯 使用流程

1. 访问 http://localhost:3000
2. 使用测试账号登录或注册新账号
3. 登录成功后自动跳转到Dashboard
4. 在Dashboard中可以查看用户信息和测试受保护的API

## 🔧 技术栈

### 后端
- **FastAPI** - 现代Python Web框架
- **JWT** - JSON Web Token认证
- **Passlib** - 密码哈希
- **Python-JOSE** - JWT处理

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 快速构建工具
- **Vue Router** - 路由管理
- **Pinia** - 状态管理
- **Axios** - HTTP客户端

## 🔒 安全特性

- 密码使用bcrypt哈希加密
- JWT token有效期控制
- 路由级别的认证守卫
- CORS配置
- 自动token验证

## 📝 开发说明

### 添加新的受保护路由

1. 在后端添加新的API端点：
```python
@app.get("/new-protected-route")
async def new_route(current_user: User = Depends(get_current_active_user)):
    return {"message": "This is a new protected route"}
```

2. 在前端添加对应的页面和路由配置

### 自定义认证逻辑

修改 `backend/auth.py` 中的认证函数来实现自定义的用户验证逻辑。

## 🚨 注意事项

- 这是一个演示项目，生产环境需要：
  - 更换SECRET_KEY
  - 使用真实数据库
  - 添加更多安全措施
  - 配置HTTPS
  - 添加日志记录

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
